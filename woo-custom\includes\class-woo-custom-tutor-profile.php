<?php
/**
 * Tutor Profile Integration for WooCommerce My Account
 *
 * @package WooCustom
 * @subpackage TutorProfile
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * WooCustom Tutor Profile Class
 */
class WooCustom_Tutor_Profile {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Check if Tutor <PERSON> is active
        if (!$this->is_tutor_active()) {
            return;
        }
        
        // Add profile editor to WooCommerce account details page
        add_action('woocommerce_before_edit_account_form', array($this, 'add_profile_editor'));
        
        // Enqueue Tutor assets on account page
        add_action('wp_enqueue_scripts', array($this, 'enqueue_tutor_assets'));
        
        // Add AJAX handlers for photo upload/remove
        add_action('wp_ajax_woo_custom_tutor_photo_upload', array($this, 'handle_photo_upload'));
        add_action('wp_ajax_woo_custom_tutor_photo_remove', array($this, 'handle_photo_remove'));
    }
    
    /**
     * Check if Tutor LMS is active
     */
    private function is_tutor_active() {
        return function_exists('tutor') && class_exists('TUTOR\User');
    }
    
    /**
     * Add profile editor to WooCommerce account details page
     */
    public function add_profile_editor() {
        // Only show on edit-account page
        if (!is_wc_endpoint_url('edit-account')) {
            return;
        }
        
        $this->render_profile_editor();
    }
    
    /**
     * Render the profile editor
     */
    private function render_profile_editor() {
        if (!$this->is_tutor_active()) {
            return;
        }
        
        $user = wp_get_current_user();
        
        // Get profile photo data
        $profile_placeholder = apply_filters('tutor_login_default_avatar', tutor()->url . 'assets/images/profile-photo.png');
        $profile_photo_src = $profile_placeholder;
        $profile_photo_id = get_user_meta($user->ID, '_tutor_profile_photo', true);
        if ($profile_photo_id) {
            $url = wp_get_attachment_image_url($profile_photo_id, 'full');
            if (!empty($url)) {
                $profile_photo_src = $url;
            }
        }
        
        // Get cover photo data
        $cover_placeholder = tutor()->url . 'assets/images/cover-photo.jpg';
        $cover_photo_src = $cover_placeholder;
        $cover_photo_id = get_user_meta($user->ID, '_tutor_cover_photo', true);
        if ($cover_photo_id) {
            $url = wp_get_attachment_image_url($cover_photo_id, 'full');
            if (!empty($url)) {
                $cover_photo_src = $url;
            }
        }
        
        // Get max file size
        $max_filesize = wp_max_upload_size();
        
        ?>
        <div class="woo-custom-tutor-profile-section">
            <h3><?php esc_html_e('Profil ve Banner Fotoğrafları', 'woo-custom'); ?></h3>
            
            <div id="tutor_profile_cover_photo_editor" class="woo-custom-profile-editor">
                <input id="tutor_photo_dialogue_box" type="file" accept=".png,.jpg,.jpeg" style="display: none;"/>
                <input type="hidden" class="upload_max_filesize" value="<?php echo esc_attr($max_filesize); ?>">
                
                <!-- Cover Photo Area -->
                <div id="tutor_cover_area" data-fallback="<?php echo esc_attr($cover_placeholder); ?>" style="background-image:url(<?php echo esc_url($cover_photo_src); ?>)">
                    <span class="tutor_cover_deleter" style="<?php echo ($cover_photo_id ? '' : 'display:none;'); ?>">
                        <span class="dashboard-profile-delete tutor-icon-trash-can-bold"></span>
                    </span>
                    <div class="tutor_overlay">
                        <button type="button" class="tutor_cover_uploader tutor-btn tutor-btn-primary">
                            <i class="tutor-icon-camera tutor-mr-12" aria-hidden="true"></i>
                            <span><?php echo $cover_photo_id ? esc_html__('Banner Fotoğrafını Güncelle', 'woo-custom') : esc_html__('Banner Fotoğrafı Yükle', 'woo-custom'); ?></span>
                        </button>
                    </div>
                </div>
                
                <!-- Photo Info -->
                <div id="tutor_photo_meta_area">
                    <span><?php esc_html_e('Profil Fotoğrafı Boyutu', 'woo-custom'); ?>: <span>200x200</span> <?php esc_html_e('piksel', 'woo-custom'); ?></span>
                    <span>&nbsp;&nbsp;&nbsp;&nbsp;<?php esc_html_e('Banner Fotoğrafı Boyutu', 'woo-custom'); ?>: <span>700x430</span> <?php esc_html_e('piksel', 'woo-custom'); ?></span>
                    <span class="loader-area" style="display: none;"><?php esc_html_e('Kaydediliyor...', 'woo-custom'); ?></span>
                </div>
                
                <!-- Profile Photo Container -->
                <div class="tutor-profile-container" style="position: relative;">
                    <!-- Profile Photo Area -->
                    <div id="tutor_profile_area" data-fallback="<?php echo esc_attr($profile_placeholder); ?>" style="background-image:url(<?php echo esc_url($profile_photo_src); ?>)">
                        <div class="tutor_overlay">
                            <i class="tutor-icon-camera"></i>
                        </div>
                    </div>

                    <!-- Profile Photo Options -->
                    <div id="tutor_pp_option">
                        <div class="up-arrow">
                            <i></i>
                        </div>
                        <span class="tutor_pp_uploader profile-uploader">
                            <i class="profile-upload-icon tutor-icon-image-landscape tutor-mr-4"></i> <?php esc_html_e('Profili Güncelle', 'woo-custom'); ?>
                        </span>
                        <span class="tutor_pp_deleter profile-uploader" style="<?php echo ($profile_photo_id ? '' : 'display:none;'); ?>">
                            <i class="profile-upload-icon tutor-icon-trash-can-bold tutor-mr-4"></i> <?php esc_html_e('Profili Sil', 'woo-custom'); ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <style>
        .woo-custom-tutor-profile-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .woo-custom-tutor-profile-section h3 {
            margin-top: 0;
            margin-bottom: 20px;
            color: #333;
        }
        </style>
        <?php
    }
    
    /**
     * Enqueue Tutor assets on account page
     */
    public function enqueue_tutor_assets() {
        // Only load on my account page
        if (!is_account_page()) {
            return;
        }
        
        if (!$this->is_tutor_active()) {
            return;
        }
        
        // Enqueue Tutor frontend CSS
        if (file_exists(WP_PLUGIN_DIR . '/tutor/assets/css/tutor-frontend-dashboard.min.css')) {
            wp_enqueue_style(
                'tutor-frontend-dashboard',
                plugins_url('tutor/assets/css/tutor-frontend-dashboard.min.css'),
                array(),
                '1.0.0'
            );
        }
        
        // Enqueue our custom JavaScript for profile handling
        wp_enqueue_script(
            'woo-custom-tutor-profile',
            WOO_CUSTOM_PLUGIN_URL . 'assets/js/tutor-profile.js',
            array('jquery'),
            WOO_CUSTOM_VERSION,
            true
        );
        
        // Localize script for AJAX
        wp_localize_script('woo-custom-tutor-profile', 'woo_custom_tutor', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('woo_custom_tutor_nonce'),
            'i18n' => array(
                'uploading' => __('Yükleniyor...', 'woo-custom'),
                'upload_error' => __('Yükleme hatası', 'woo-custom'),
                'delete_confirm' => __('Bu fotoğrafı silmek istediğinizden emin misiniz?', 'woo-custom'),
                'success' => __('Başarılı', 'woo-custom'),
                'error' => __('Hata', 'woo-custom'),
            )
        ));
    }
    
    /**
     * Handle photo upload via AJAX
     */
    public function handle_photo_upload() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'woo_custom_tutor_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        if (!is_user_logged_in()) {
            wp_send_json_error('User not logged in');
        }
        
        $user_id = get_current_user_id();
        $photo_type = sanitize_text_field($_POST['photo_type']);
        $meta_key = 'cover_photo' === $photo_type ? '_tutor_cover_photo' : '_tutor_profile_photo';
        
        // Handle file upload
        $photo = $_FILES['photo_file'];
        $photo_size = $photo['size'];
        $photo_type_mime = $photo['type'];
        
        if ($photo_size && strpos($photo_type_mime, 'image') !== false) {
            if (!function_exists('wp_handle_upload')) {
                require_once ABSPATH . 'wp-admin/includes/file.php';
            }
            
            $upload_overrides = array('test_form' => false);
            $movefile = wp_handle_upload($photo, $upload_overrides);
            
            if ($movefile && !isset($movefile['error'])) {
                $file_path = $movefile['file'];
                $file_url = $movefile['url'];
                $mime_type = '';
                
                if (file_exists($file_path)) {
                    $image_info = getimagesize($file_path);
                    $mime_type = is_array($image_info) && count($image_info) ? $image_info['mime'] : '';
                }
                
                $media_id = wp_insert_attachment(
                    array(
                        'guid' => $file_path,
                        'post_mime_type' => $mime_type,
                        'post_title' => preg_replace('/\.[^.]+$/', '', basename($file_url)),
                        'post_content' => '',
                        'post_status' => 'inherit',
                    ),
                    $file_path,
                    0
                );
                
                if ($media_id) {
                    // Generate attachment metadata
                    require_once ABSPATH . 'wp-admin/includes/image.php';
                    wp_update_attachment_metadata($media_id, wp_generate_attachment_metadata($media_id, $file_path));
                    
                    // Delete existing photo
                    $this->delete_existing_user_photo($user_id, sanitize_text_field($_POST['photo_type']));
                    
                    // Update user meta
                    update_user_meta($user_id, $meta_key, $media_id);
                    
                    wp_send_json_success(array(
                        'message' => 'Photo uploaded successfully',
                        'url' => $file_url
                    ));
                }
            }
        }
        
        wp_send_json_error('Upload failed');
    }
    
    /**
     * Handle photo removal via AJAX
     */
    public function handle_photo_remove() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'woo_custom_tutor_nonce')) {
            wp_send_json_error('Security check failed');
            return;
        }
        
        if (!is_user_logged_in()) {
            wp_send_json_error('User not logged in');
        }
        
        $user_id = get_current_user_id();
        $photo_type = sanitize_text_field($_POST['photo_type']);
        
        $this->delete_existing_user_photo($user_id, $photo_type);
        
        wp_send_json_success('Photo removed successfully');
    }
    
    /**
     * Delete existing user photo
     */
    private function delete_existing_user_photo($user_id, $type) {
        $meta_key = 'cover_photo' == $type ? '_tutor_cover_photo' : '_tutor_profile_photo';
        $photo_id = get_user_meta($user_id, $meta_key, true);
        
        if (is_numeric($photo_id)) {
            wp_delete_attachment($photo_id, true);
        }
        
        delete_user_meta($user_id, $meta_key);
    }
}
