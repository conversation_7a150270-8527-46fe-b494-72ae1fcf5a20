/**
 * Tutor Profile Integration JavaScript
 * Handles profile and cover photo upload/delete functionality
 */

jQuery(document).ready(function($) {
    'use strict';

    var currentPhotoType = '';
    var isFileDialogOpen = false;

    // Cover photo uploader click
    $(document).on('click', '.tutor_cover_uploader', function(e) {
        e.preventDefault();
        e.stopPropagation();

        if (isFileDialogOpen) {
            return false;
        }

        currentPhotoType = 'cover_photo';
        isFileDialogOpen = true;

        var fileInput = $('#tutor_photo_dialogue_box')[0];
        fileInput.click();

        // Reset flag after a short delay
        setTimeout(function() {
            isFileDialogOpen = false;
        }, 500);
    });

    // Profile photo area click
    $(document).on('click', '#tutor_profile_area', function(e) {
        e.preventDefault();
        e.stopPropagation();
        currentPhotoType = 'profile_photo';

        // Hide if already visible, show if hidden
        var $menu = $('#tutor_pp_option');
        if ($menu.is(':visible')) {
            $menu.css('display', 'none');
        } else {
            $menu.css('display', 'block');
        }
    });

    // Profile photo uploader click
    $(document).on('click', '.tutor_pp_uploader', function(e) {
        e.preventDefault();
        e.stopPropagation();

        if (isFileDialogOpen) {
            return false;
        }

        currentPhotoType = 'profile_photo';
        isFileDialogOpen = true;

        var fileInput = $('#tutor_photo_dialogue_box')[0];
        fileInput.click();
        $('#tutor_pp_option').css('display', 'none');

        // Reset flag after a short delay
        setTimeout(function() {
            isFileDialogOpen = false;
        }, 500);
    });
    
    // Profile photo deleter click
    $(document).on('click', '.tutor_pp_deleter', function(e) {
        e.preventDefault();
        if (confirm(woo_custom_tutor.i18n.delete_confirm)) {
            deletePhoto('profile_photo');
        }
        $('#tutor_pp_option').css('display', 'none');
    });
    
    // Cover photo deleter click
    $(document).on('click', '.tutor_cover_deleter', function(e) {
        e.preventDefault();
        if (confirm(woo_custom_tutor.i18n.delete_confirm)) {
            deletePhoto('cover_photo');
        }
    });
    
    // Hide profile options when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('#tutor_profile_area, #tutor_pp_option').length) {
            $('#tutor_pp_option').css('display', 'none');
        }
    });
    
    // File input change event
    $('#tutor_photo_dialogue_box').on('change', function(e) {
        var file = this.files[0];
        if (file) {
            uploadPhoto(file, currentPhotoType);
        }
        // Reset the input value to allow selecting the same file again
        $(this).val('');
        isFileDialogOpen = false;
    });

    // File input focus/blur events to handle dialog state
    $('#tutor_photo_dialogue_box').on('focus', function() {
        isFileDialogOpen = true;
    }).on('blur', function() {
        setTimeout(function() {
            isFileDialogOpen = false;
        }, 300);
    });
    
    /**
     * Upload photo function
     */
    function uploadPhoto(file, photoType) {
        // Validate file type
        if (!file.type.match('image.*')) {
            alert(woo_custom_tutor.i18n.upload_error + ': Invalid file type');
            return;
        }
        
        // Validate file size
        var maxSize = parseInt($('.upload_max_filesize').val());
        if (file.size > maxSize) {
            alert(woo_custom_tutor.i18n.upload_error + ': File too large');
            return;
        }
        
        // Show loading
        showLoading(true);
        
        // Prepare form data
        var formData = new FormData();
        formData.append('action', 'woo_custom_tutor_photo_upload');
        formData.append('photo_file', file);
        formData.append('photo_type', photoType);
        formData.append('nonce', woo_custom_tutor.nonce);
        
        // Upload via AJAX
        $.ajax({
            url: woo_custom_tutor.ajax_url,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                showLoading(false);
                
                if (response.success) {
                    // Update the image
                    updatePhotoDisplay(response.data.url, photoType);
                    
                    // Show delete button
                    if (photoType === 'cover_photo') {
                        $('.tutor_cover_deleter').show();
                        $('.tutor_cover_uploader span').text('Banner Fotoğrafını Güncelle');
                    } else {
                        $('.tutor_pp_deleter').show();
                    }
                    
                    // Hide profile options
                    $('#tutor_pp_option').css('display', 'none');
                    
                } else {
                    alert(woo_custom_tutor.i18n.upload_error + ': ' + (response.data || 'Unknown error'));
                }
            },
            error: function() {
                showLoading(false);
                alert(woo_custom_tutor.i18n.upload_error);
            }
        });
    }
    
    /**
     * Delete photo function
     */
    function deletePhoto(photoType) {
        // Show loading
        showLoading(true);
        
        $.ajax({
            url: woo_custom_tutor.ajax_url,
            type: 'POST',
            data: {
                action: 'woo_custom_tutor_photo_remove',
                photo_type: photoType,
                nonce: woo_custom_tutor.nonce
            },
            success: function(response) {
                showLoading(false);
                
                if (response.success) {
                    // Reset to placeholder
                    var placeholder = '';
                    if (photoType === 'cover_photo') {
                        placeholder = $('#tutor_cover_area').data('fallback');
                        $('#tutor_cover_area').css('background-image', 'url(' + placeholder + ')');
                        $('.tutor_cover_deleter').hide();
                        $('.tutor_cover_uploader span').text('Banner Fotoğrafı Yükle');
                    } else {
                        placeholder = $('#tutor_profile_area').data('fallback');
                        $('#tutor_profile_area').css('background-image', 'url(' + placeholder + ')');
                        $('.tutor_pp_deleter').hide();
                    }
                } else {
                    alert(woo_custom_tutor.i18n.error + ': ' + (response.data || 'Unknown error'));
                }
            },
            error: function() {
                showLoading(false);
                alert(woo_custom_tutor.i18n.error);
            }
        });
    }
    
    /**
     * Update photo display
     */
    function updatePhotoDisplay(url, photoType) {
        if (photoType === 'cover_photo') {
            $('#tutor_cover_area').css('background-image', 'url(' + url + ')');
        } else {
            $('#tutor_profile_area').css('background-image', 'url(' + url + ')');
        }
    }
    
    /**
     * Show/hide loading indicator
     */
    function showLoading(show) {
        if (show) {
            $('.loader-area').show();
        } else {
            $('.loader-area').hide();
        }
    }
});
