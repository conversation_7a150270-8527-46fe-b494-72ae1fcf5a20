/**
 * Woo Custom Plugin Styles
 */

/* Wishlist <PERSON><PERSON> Styles */
.woo-custom-wishlist-btn {
    position: relative;
    background: none;
    border: none;
    cursor: pointer;
    width: 40px;
    height: 40px;
    padding: 0;
    border-radius: 50%;
    transition: all 0.3s ease;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Heart icon styles */
.woo-custom-wishlist-btn .heart-icon,
.woo-custom-wishlist-btn .heart-icon-filled {
    font-size: 20px;
    transition: all 0.3s ease;
    display: inline-block;
}

.woo-custom-wishlist-btn .heart-icon {
    color: #999;
}

.woo-custom-wishlist-btn .heart-icon-filled {
    color: #e74c3c;
    display: none;
}

/* Hover states */
.woo-custom-wishlist-btn:hover .heart-icon {
    color: #e74c3c;
    transform: scale(1.1);
}

/* Active state (in wishlist) */
.woo-custom-wishlist-btn.in-wishlist .heart-icon {
    display: none;
}

.woo-custom-wishlist-btn.in-wishlist .heart-icon-filled {
    display: inline-block;
    animation: heartBeat 0.6s ease-in-out;
}

/* Heart beat animation */
@keyframes heartBeat {
    0% { transform: scale(1); }
    25% { transform: scale(1.2); }
    50% { transform: scale(1); }
    75% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Product loop positioning */
.woocommerce ul.products li.product .woo-custom-wishlist-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.woocommerce ul.products li.product:hover .woo-custom-wishlist-btn {
    opacity: 1;
    transform: translateY(0);
}

/* Single product page wishlist button */
.woo-custom-wishlist-wrapper {
    margin: 15px 0;
}

.woo-custom-wishlist-btn.single-product {
    background: none;
    border: none;
    padding: 0;
    border-radius: 0;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #333;
    text-decoration: none;
    cursor: pointer;
    margin-left: 30px;
    white-space: nowrap;
    width: 110px;
    outline: none;
}

.woo-custom-wishlist-btn.single-product:focus {
    outline: none;
    box-shadow: none;
}

.woo-custom-wishlist-btn.single-product:hover {
    background: none;
    color: #333;
    border: none;
}

.woo-custom-wishlist-btn.single-product:hover .heart-icon {
    color: #e74c3c;
}

.woo-custom-wishlist-btn.single-product.in-wishlist {
    background: none;
    color: #333;
    border: none;
}

.woo-custom-wishlist-btn.single-product .wishlist-text {
    font-weight: 500;
    margin-left: 4px;
}

.woo-custom-wishlist-btn.single-product .heart-icon,
.woo-custom-wishlist-btn.single-product .heart-icon-filled {
    font-size: 18px;
    margin-right: 4px;
}

/* Loading state */
.woo-custom-wishlist-btn.loading {
    opacity: 0.6;
    pointer-events: none;
}

.woo-custom-wishlist-btn.loading .heart-icon,
.woo-custom-wishlist-btn.loading .heart-icon-filled {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Login required state */
.woo-custom-wishlist-btn.requires-login {
    opacity: 0.7;
}

.woo-custom-wishlist-btn.requires-login:hover {
    opacity: 0.9;
}

/* Notification styles */
.woo-custom-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #333;
    color: white;
    padding: 12px 20px;
    border-radius: 4px;
    z-index: 9999;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.woo-custom-notification.show {
    opacity: 1;
    transform: translateX(0);
}

.woo-custom-notification.success {
    background: #27ae60;
}

.woo-custom-notification.error {
    background: #e74c3c;
}

/* My Account menu item icons - More specific selectors */
.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--wishlist a:before,
.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--my-reviews a:before {
    display: inline-block !important;
    margin-left: 4px !important;
    text-align: center !important;
    line-height: 24px !important;
    font-size: 20px !important;
    vertical-align: middle !important;
    font-style: normal !important;
    font-weight: normal !important;
}

.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--wishlist a:before {
    content: "♡" !important;
}

.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--my-reviews a:before {
    content: "☆" !important;
}

/* Hover effects for menu icons */
.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--wishlist a:hover:before {
    content: "♥" !important;
}

.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--my-reviews a:hover:before {
    content: "★" !important;
}



/* Responsive design */
@media (max-width: 768px) {
    .woocommerce ul.products li.product .woo-custom-wishlist-btn {
        opacity: 1;
        transform: translateY(0);
        top: 5px;
        right: 5px;
        padding: 6px;
    }
    
    .woo-custom-wishlist-btn .heart-icon,
    .woo-custom-wishlist-btn .heart-icon-filled {
        font-size: 18px;
    }
    
    .woo-custom-notification {
        top: 10px;
        right: 10px;
        left: 10px;
        transform: translateY(-100%);
    }
    
    .woo-custom-notification.show {
        transform: translateY(0);
    }
}

/* Product grid compatibility */
.woocommerce ul.products li.product {
    position: relative;
}

/* Theme compatibility adjustments */
.woocommerce ul.products li.product .woo-custom-wishlist-btn {
    z-index: 10;
}

/* Ensure proper positioning in different themes */
.products .product .woo-custom-wishlist-btn,
.wc-block-grid__products .wc-block-grid__product .woo-custom-wishlist-btn {
    position: absolute;
    top: 10px;
    right: 10px;
}

/* WooCommerce Blocks compatibility */
.wc-block-grid__product {
    position: relative;
}

.wc-block-grid__product .woo-custom-wishlist-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.9);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    opacity: 0;
    transition: all 0.3s ease;
}

.wc-block-grid__product:hover .woo-custom-wishlist-btn {
    opacity: 1;
}

/* Accessibility improvements */
.woo-custom-wishlist-btn:focus {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

.woo-custom-wishlist-btn[aria-pressed="true"] .heart-icon {
    display: none;
}

.woo-custom-wishlist-btn[aria-pressed="true"] .heart-icon-filled {
    display: inline-block;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .woo-custom-wishlist-btn {
        border: 2px solid currentColor;
    }
    
    .woo-custom-wishlist-btn .heart-icon {
        color: currentColor;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .woo-custom-wishlist-btn,
    .woo-custom-wishlist-btn .heart-icon,
    .woo-custom-wishlist-btn .heart-icon-filled,
    .woo-custom-notification {
        transition: none;
        animation: none;
    }
}

/* Tutor Profile Integration Styles */
.woo-custom-tutor-profile-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background: #f9f9f9;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.woo-custom-tutor-profile-section h3 {
    margin-top: 0;
    margin-bottom: 20px;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

/* Profile Editor Container */
.woo-custom-profile-editor {
    position: relative;
    background: #fff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Cover Photo Area */
#tutor_cover_area {
    position: relative;
    height: 200px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    cursor: pointer;
    overflow: hidden;
}

#tutor_cover_area .tutor_overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

#tutor_cover_area:hover .tutor_overlay {
    opacity: 1;
}

.tutor_cover_uploader {
    background: #007cba;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background 0.3s ease;
}

.tutor_cover_uploader:hover {
    background: #005a87;
}

.tutor_cover_deleter {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background 0.3s ease;
}

.tutor_cover_deleter:hover {
    background: #fff;
}

.tutor_cover_deleter .dashboard-profile-delete {
    color: #dc3545;
    font-size: 16px;
}

/* Photo Meta Area */
#tutor_photo_meta_area {
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    font-size: 13px;
    color: #666;
    text-align: center;
}

#tutor_photo_meta_area .loader-area {
    color: #007cba;
    font-weight: 500;
}

/* Profile Photo Container */
.tutor-profile-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* Profile Photo Area */
#tutor_profile_area {
    position: relative;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    margin: -60px auto 20px;
    border: 4px solid #fff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    cursor: pointer;
    overflow: hidden;
}

#tutor_profile_area .tutor_overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 50%;
}

#tutor_profile_area:hover .tutor_overlay {
    opacity: 1;
}

#tutor_profile_area .tutor_overlay i {
    color: white;
    font-size: 24px;
}

/* Profile Photo Options */
#tutor_pp_option {
    position: absolute;
    top: 80px;
    left: 50%;
    transform: translateX(-50%);
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 5px;
    z-index: 9999;
    min-width: 180px;
    display: none;
    margin-top: 10px;
}

#tutor_pp_option .up-arrow {
    position: absolute;
    top: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid white;
}

#tutor_pp_option .up-arrow:before {
    content: '';
    position: absolute;
    top: 1px;
    left: -8px;
    width: 0;
    height: 0;
    border-left: 8px solid transparent;
    border-right: 8px solid transparent;
    border-bottom: 8px solid #ddd;
}

.profile-uploader {
    display: flex;
    padding: 10px 15px;
    color: #333;
    text-decoration: none;
    cursor: pointer;
    border-radius: 4px;
    transition: background 0.3s ease;
    font-size: 14px;
    align-items: center;
    gap: 8px;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
}

.profile-uploader:hover {
    background: #f8f9fa;
    color: #333;
}

.profile-uploader i {
    font-size: 16px;
}

/* Tutor Icons */
.tutor-icon-camera:before {
    content: "📷";
    font-style: normal;
}

.tutor-icon-image-landscape:before {
    content: "🖼️";
    font-style: normal;
}

.tutor-icon-trash-can-bold:before {
    content: "🗑️";
    font-style: normal;
}

/* Responsive Design */
@media (max-width: 768px) {
    .woo-custom-tutor-profile-section {
        padding: 15px;
        margin-bottom: 20px;
    }

    #tutor_cover_area {
        height: 150px;
    }

    #tutor_profile_area {
        width: 100px;
        height: 100px;
        margin: -50px auto 15px;
    }

    #tutor_photo_meta_area {
        padding: 10px 15px;
        font-size: 12px;
    }

    .tutor_cover_uploader {
        padding: 10px 16px;
        font-size: 13px;
    }

    #tutor_pp_option {
        min-width: 160px;
    }
}
